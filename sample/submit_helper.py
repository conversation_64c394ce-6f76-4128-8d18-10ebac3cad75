
def submit_answer(student_no, question_id, fig=None, result_value=None, note="", runtime=None):

    """
    提交答案到服务器

    参数:
        student_no (str): 学号，如 '2022062030'
        question_id (int): 题目ID
        fig (matplotlib.figure.Figure): matplotlib图形对象（可选）
        result_value (float): 计算结果数值（可选）
        note (str): 备注说明
        runtime (float): 运行时间（秒），用于性能评估（可选）

    返回:
        dict: 提交结果 {'success': bool, 'message': str}
    """

    import requests
    import json
    import matplotlib.pyplot as plt
    from io import BytesIO
    import numpy as np
    SERVER_URL = "http://43.155.146.157:30200"  # 生产环境
    try:
        # 准备提交数据
        data = {
            'student_no': student_no,
            'question_id': question_id,
            'note': note
        }

        # 添加运行时间
        if runtime is not None:
            data['runtime'] = runtime

        files = {}

        # 如果有matplotlib图形对象，直接保存
        if fig is not None:
            # 保存图像到内存
            buffer = BytesIO()
            fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)

            # 添加图像文件
            files['image'] = ('result.png', buffer.getvalue(), 'image/png')

        # 如果有结果数值
        if result_value is not None:
            data['numeric'] = json.dumps([result_value])
        
        # 发送请求
        response = requests.post(f"{SERVER_URL}/api/submissions", 
                               files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 提交成功! 学号: {student_no}, 题目: {question_id}")
            if note:
                print(f"   备注: {note}")
            if runtime is not None:
                print(f"   运行时间: {runtime:.3f}秒")
            return {'success': True, 'message': '提交成功'}
        else:
            error_msg = f"提交失败: {response.status_code}"
            try:
                error_detail = response.json().get('detail', '未知错误')
                error_msg += f" - {error_detail}"
            except:
                pass
            print(f"❌ {error_msg}")
            return {'success': False, 'message': error_msg}
            
    except requests.exceptions.Timeout:
        error_msg = "提交超时，请检查网络连接"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}
    except requests.exceptions.ConnectionError:
        error_msg = "无法连接到服务器，请检查网络或服务器状态"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}
    except Exception as e:
        error_msg = f"提交出错: {str(e)}"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}
# 使用示例

