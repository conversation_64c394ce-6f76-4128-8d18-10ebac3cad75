#!/usr/bin/env python3
"""
性能测试示例
测试新的运行时间和性能评分功能
"""

import time
import matplotlib.pyplot as plt
import numpy as np
from submit_helper import submit_answer

def test_fast_calculation():
    """快速计算测试"""
    print("🚀 开始快速计算测试...")
    start_time = time.time()
    
    # 简单的计算
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.plot(x, y, 'b-', linewidth=2, label='sin(x)')
    ax.set_title('快速计算 - 正弦函数')
    ax.set_xlabel('x')
    ax.set_ylabel('sin(x)')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    runtime = time.time() - start_time
    
    # 提交结果
    result = submit_answer(
        student_no='2022121001',
        question_id=1,
        fig=fig,
        runtime=runtime,
        note=f'快速计算测试 - 运行时间: {runtime:.3f}秒'
    )
    
    plt.close(fig)
    return result

def test_slow_calculation():
    """慢速计算测试"""
    print("🐌 开始慢速计算测试...")
    start_time = time.time()
    
    # 故意添加延迟
    time.sleep(2.5)
    
    # 复杂一些的计算
    x = np.linspace(0, 4*np.pi, 1000)
    y1 = np.sin(x)
    y2 = np.cos(x)
    y3 = np.sin(x) * np.cos(x)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.plot(x, y1, 'r-', linewidth=2, label='sin(x)')
    ax.plot(x, y2, 'g-', linewidth=2, label='cos(x)')
    ax.plot(x, y3, 'b-', linewidth=2, label='sin(x)*cos(x)')
    ax.set_title('慢速计算 - 三角函数组合')
    ax.set_xlabel('x')
    ax.set_ylabel('y')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    runtime = time.time() - start_time
    
    # 提交结果
    result = submit_answer(
        student_no='2022121002',
        question_id=1,
        fig=fig,
        runtime=runtime,
        note=f'慢速计算测试 - 运行时间: {runtime:.3f}秒'
    )
    
    plt.close(fig)
    return result

def test_medium_calculation():
    """中等速度计算测试"""
    print("⚡ 开始中等速度计算测试...")
    start_time = time.time()
    
    # 中等复杂度的计算
    time.sleep(1.2)
    
    # 生成一些数据
    t = np.linspace(0, 10, 500)
    signal = np.sin(2*np.pi*t) * np.exp(-t/5)
    noise = 0.1 * np.random.randn(len(t))
    noisy_signal = signal + noise
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
    
    ax1.plot(t, signal, 'b-', linewidth=2, label='原始信号')
    ax1.set_title('原始信号')
    ax1.set_ylabel('幅度')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    ax2.plot(t, noisy_signal, 'r-', linewidth=1, alpha=0.7, label='含噪声信号')
    ax2.set_title('含噪声信号')
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('幅度')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    
    runtime = time.time() - start_time
    
    # 提交结果
    result = submit_answer(
        student_no='2022121003',
        question_id=1,
        fig=fig,
        runtime=runtime,
        note=f'中等速度计算测试 - 运行时间: {runtime:.3f}秒'
    )
    
    plt.close(fig)
    return result

def test_numerical_result():
    """数值结果测试"""
    print("🔢 开始数值结果测试...")
    start_time = time.time()
    
    # 计算圆周率
    n = 1000000
    inside_circle = 0
    
    for _ in range(n):
        x = np.random.random()
        y = np.random.random()
        if x*x + y*y <= 1:
            inside_circle += 1
    
    pi_estimate = 4 * inside_circle / n
    runtime = time.time() - start_time
    
    # 提交结果
    result = submit_answer(
        student_no='2022121004',
        question_id=1,
        result_value=pi_estimate,
        runtime=runtime,
        note=f'蒙特卡洛计算π - 估计值: {pi_estimate:.6f}, 运行时间: {runtime:.3f}秒'
    )
    
    return result

if __name__ == "__main__":
    print("🧪 性能测试开始")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("快速计算", test_fast_calculation),
        ("中等速度计算", test_medium_calculation),
        ("慢速计算", test_slow_calculation),
        ("数值结果", test_numerical_result)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name} 完成")
        except Exception as e:
            print(f"❌ {test_name} 失败: {e}")
            results.append((test_name, {'success': False, 'message': str(e)}))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 成功" if result.get('success') else "❌ 失败"
        print(f"  {test_name}: {status}")
    
 
    print("  课堂展示: http://localhost:30200/static/classroom_display.html")
    print("\n💡 在课堂展示中，您可以:")
    print("  - 按性能排序查看不同运行时间的提交")