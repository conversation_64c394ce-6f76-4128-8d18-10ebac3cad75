#!/usr/bin/env python3
"""
测试 submit_helper.py 的功能
"""

import matplotlib.pyplot as plt
import numpy as np
from submit_helper import submit_answer

# 创建一个简单的测试图形
def create_test_plot():
    """创建一个简单的测试图形"""
    x = np.linspace(0, 10, 100)
    y = np.sin(x) * np.exp(-x/5)
    
    plt.figure(figsize=(8, 6))
    plt.plot(x, y, 'b-', linewidth=2, label='衰减正弦波')
    plt.xlabel('时间 (s)')
    plt.ylabel('振幅')
    plt.title('衰减正弦波测试')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    return plt.gcf()

if __name__ == "__main__":
    print("测试 submit_helper.py 功能...")
    
    # 创建测试图形
    fig = create_test_plot()
    
    # 测试提交（使用题目ID 15，这是一个已发布并开放的题目）
    result = submit_answer(
        student_no='2022002',
        question_id=15,
        fig=fig,
        result_value=3.14159,
        note='submit_helper.py 测试提交',
        runtime=0.123
    )
    
    if result['success']:
        print("✅ submit_helper.py 测试成功！")
    else:
        print(f"❌ submit_helper.py 测试失败: {result['message']}")
    
    plt.close(fig)  # 关闭图形以释放内存
