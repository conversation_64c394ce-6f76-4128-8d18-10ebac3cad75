import math
import matplotlib.pyplot as plt
import requests
import json
from io import BytesIO
import numpy as np # 导入 numpy 用于 np.arange

# ====================================================================
# submit_answer 函数集成开始
# ====================================================================

# 服务器配置
SERVER_URL = "http://localhost:30200"  # 本地开发环境

def submit_answer(student_no, question_id, fig=None, result_value=None, note="", runtime=None):
    """
    提交答案到服务器

    参数:
        student_no (str): 学号，如 '2022062030'
        question_id (int): 题目ID
        fig (matplotlib.figure.Figure): matplotlib图形对象（可选）
        result_value (float): 计算结果数值（可选）
        note (str): 备注说明
        runtime (float): 运行时间（秒），用于性能评估（可选）

    返回:
        dict: 提交结果 {'success': bool, 'message': str}
    """
    
    try:
        # 准备提交数据
        data = {
            'student_no': student_no,
            'question_id': question_id,
            'note': note
        }

        # 添加运行时间
        if runtime is not None:
            data['runtime'] = runtime

        files = {}

        # 如果有matplotlib图形对象，直接保存
        if fig is not None:
            # 保存图像到内存
            buffer = BytesIO()
            fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)

            # 添加图像文件
            files['image'] = ('result.png', buffer.getvalue(), 'image/png')

        # 如果有结果数值
        if result_value is not None:
            data['numeric'] = json.dumps([result_value])
        
        # 发送请求
        response = requests.post(f"{SERVER_URL}/api/submissions", 
                               files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 提交成功! 学号: {student_no}, 题目: {question_id}")
            if note:
                print(f"   备注: {note}")
            if runtime is not None:
                print(f"   运行时间: {runtime:.3f}秒")
            return {'success': True, 'message': '提交成功'}
        else:
            error_msg = f"提交失败: {response.status_code}"
            try:
                error_detail = response.json().get('detail', '未知错误')
                error_msg += f" - {error_detail}"
            except:
                pass
            print(f"❌ {error_msg}")
            return {'success': False, 'message': error_msg}
            
    except requests.exceptions.Timeout:
        error_msg = "提交超时，请检查网络连接"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}
    except requests.exceptions.ConnectionError:
        error_msg = "无法连接到服务器，请检查网络或服务器状态"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}
    except Exception as e:
        error_msg = f"提交出错: {str(e)}"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}

# ====================================================================
# submit_answer 函数集成结束
# ====================================================================


# 定义全局常量
v0 = 700
k = 4.0e-5
dt = 0.1
g = 9.8

def calculate_range(angle):
    """
    计算给定发射角度下的射程。
    考虑了空气阻力。
    """
    x = 0.0
    y = 0.0
    
    # 将角度转换为弧度
    angle_rad = angle * math.pi / 180
    
    # 计算初始速度分量
    vx = v0 * math.cos(angle_rad)
    vy = v0 * math.sin(angle_rad)
    
    # 模拟弹道轨迹，直到物体落地（y < 0）
    while y >= 0:
        prev_y = y
        prev_x = x
        
        # 计算当前速度的模
        v = math.sqrt(vx * vx + vy * vy)
        
        # 更新位置
        x = x + vx * dt
        y = y + vy * dt
        
        # 更新速度分量，考虑空气阻力和重力
        vx = vx - k * v * vx * dt
        vy = vy - g * dt - k * v * vy * dt
    
    # 进行线性插值，精确计算落地点的x坐标
    # 当 y 首次变为负数时，表示物体已经穿过地面
    # prev_y 是落地前最后一个正的y坐标
    # y 是落地后第一个负的y坐标
    r = -prev_y / (y - prev_y)
    range_val = prev_x + r * (x - prev_x)
    
    return range_val

# 主程序
if __name__ == "__main__":
    angle_data = []
    range_data = []

    # 循环计算不同角度下的射程
    for angle in range(0, 91, 1): # 从0到90，步长为1
        current_range = calculate_range(angle)
        angle_data.append(angle)
        range_data.append(current_range)

    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.plot(angle_data, range_data, marker='o', linestyle='-', markersize=4)
    plt.title('Projectile Range vs. Launch Angle (with Air Resistance)')
    plt.xlabel('Launch Angle (degrees)')
    plt.ylabel('Range (m)')
    plt.grid(True)
    plt.xticks(np.arange(0, 91, 5)) # 设置x轴刻度，每5度一个
    plt.yticks(np.arange(0, max(range_data) * 1.1, 1000)) # 设置y轴刻度
    plt.tight_layout()

    # 找到最大射程及其对应的角度
    max_range = 0
    optimal_angle = 0
    for i in range(len(range_data)):
        if range_data[i] > max_range:
            max_range = range_data[i]
            optimal_angle = angle_data[i]
    
    plt.plot(optimal_angle, max_range, 'ro', markersize=8, label=f'Max Range: {max_range:.2f}m at {optimal_angle}°')
    plt.legend()

    # 显示图形 (在提交时通常不需要显示，但调试时有用)
    # plt.show() 

    # 使用 submit_answer 提交图片
    # 使用题目ID 15（已发布并开放的题目）
    submit_answer(
        student_no='2022001',
        question_id=15,
        fig=plt.gcf(), # 获取当前图形对象
        note=f'弹道计算结果，最大射程：{max_range:.2f}m @ {optimal_angle}°'
    )
