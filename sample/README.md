# 📚 计算物理课程示例程序

本文件夹包含计算物理课程的示例程序，用于模拟课堂教学和学生提交。

## 📁 文件说明

### 核心工具
- **`submit_helper.py`** - 学生提交助手，包含所有提交函数
  - `submit_answer()` - 通用提交函数
  - `quick_submit()` - 快速提交当前图形

### 示例程序

#### 1. **`example1_harmonic_oscillator.py`** - 简谐振动
- **物理概念**: 简谐振动、振幅、频率、相位
- **计算内容**: 
  - 位移方程 x(t) = A·cos(ωt + φ)
  - 速度方程 v(t) = -Aω·sin(ωt + φ)
  - 周期分析
- **学习目标**: 理解简谐运动的数学描述和物理特性

#### 2. **`example2_wave_interference.py`** - 波的干涉
- **物理概念**: 波的叠加、干涉、相位差、包络线
- **计算内容**:
  - 两个相干波源的合成
  - 干涉强度分布
  - 对比度分析
- **学习目标**: 掌握波的干涉原理和数值计算方法

#### 3. **`example3_pendulum_simulation.py`** - 单摆运动模拟
- **物理概念**: 非线性振动、数值求解微分方程、相空间
- **计算内容**:
  - 非线性单摆方程求解
  - 周期与振幅关系
  - 能量守恒验证
  - 相空间轨迹
- **学习目标**: 理解非线性系统和数值方法

#### 4. **`example4_monte_carlo_pi.py`** - 蒙特卡罗方法计算π
- **物理概念**: 随机过程、统计方法、收敛性分析
- **计算内容**:
  - 随机投点算法
  - 收敛性分析
  - 误差估计
  - 统计分布
- **学习目标**: 掌握蒙特卡罗方法和统计分析

## 🚀 使用方法

### 教师端操作
1. **创建题目**: 在管理端创建对应的题目
2. **发布题目**: 选择目标班级发布题目
3. **分发代码**: 将示例程序分发给学生
4. **课堂展示**: 使用课堂展示页面实时查看学生提交

### 学生端操作
1. **获取程序**: 从教师处获得Python示例程序
2. **修改学号**: 将程序中的学号改为自己的学号
3. **理解题目**: 阅读题目要求和物理背景
4. **完成计算**: 在指定区域完成计算和分析
5. **运行提交**: 运行程序自动提交结果

## 📊 程序结构

每个示例程序都采用统一的结构：

```python
# ==================== 学生信息 ====================
STUDENT_NO = "2022062030"  # 学生修改
QUESTION_ID = 1            # 题目ID

# ==================== 题目要求 ====================
"""
详细的题目描述和要求
"""

def solve_problem():
    """
    学生解题部分 - 核心计算逻辑
    """
    # ========== 学生需要设置的参数 ==========
    # 物理参数设置
    
    # ========== 学生需要完成的计算 ==========
    # 核心计算代码
    
    # ========== 学生需要完成的绘图 ==========
    # 结果可视化
    
    return data, results

def main():
    """主函数"""
    # 调用解题函数
    data, results = solve_problem()
    
    # 显示结果
    print("计算结果...")
    
    # 自动提交
    submit_answer(STUDENT_NO, QUESTION_ID, ...)
```

## 🎯 教学目标

### 计算技能
- **数值计算**: NumPy数组操作、数学函数
- **科学绘图**: Matplotlib图形绘制、多子图布局
- **数据分析**: 统计分析、误差计算、收敛性分析
- **算法实现**: 数值积分、微分方程求解、蒙特卡罗方法

### 物理概念
- **经典力学**: 简谐振动、单摆运动、非线性系统
- **波动光学**: 波的干涉、叠加原理、相位关系
- **统计物理**: 随机过程、统计方法、概率分布
- **数值方法**: 微分方程数值解、收敛性分析

### 编程能力
- **模块化编程**: 函数设计、代码组织
- **科学计算**: SciPy库使用、数值方法
- **数据可视化**: 专业图表制作、结果展示
- **调试技能**: 错误处理、结果验证

## 🔧 技术要求

### 必需库
```bash
pip install numpy matplotlib scipy requests
```

### 服务器配置
- **生产环境**: `http://**************:30200`
- **本地测试**: `http://localhost:30200`

### 文件依赖
- 所有示例程序都依赖 `submit_helper.py`
- 确保两个文件在同一目录下

## 📈 评估标准

### 计算正确性 (40%)
- 物理公式应用正确
- 数值计算结果准确
- 参数设置合理

### 程序质量 (30%)
- 代码结构清晰
- 注释完整
- 错误处理得当

### 结果展示 (20%)
- 图形美观专业
- 数据标注完整
- 结果分析深入

### 创新性 (10%)
- 参数选择有创意
- 分析角度独特
- 额外的探索和思考

## 🎉 课堂互动

### 实时展示
- 学生提交后立即在课堂展示页面显示
- 支持多种展示模式（网格、轮播、对比）
- 教师可以选择优秀作品进行讲解

### 讨论话题
- 不同参数选择的物理意义
- 数值方法的优缺点
- 计算结果的物理解释
- 误差来源和改进方法

### 拓展练习
- 修改物理参数观察变化
- 尝试不同的数值方法
- 增加新的分析维度
- 与理论结果对比

---

## 💡 使用提示

1. **修改学号**: 每个学生必须修改程序中的学号
2. **理解物理**: 先理解物理背景再开始编程
3. **逐步调试**: 分步骤运行和验证结果
4. **美化图形**: 注意图形的专业性和美观性
5. **及时提交**: 完成后立即运行提交，避免遗忘

## 🌐 相关链接

- **课堂展示**: http://**************:30200/static/classroom_display.html
- **管理端**: http://**************:30200/static/admin.html
- **学生视图**: http://**************:30200/public/students/view
- **API文档**: http://**************:30200/docs

---

**祝学习愉快！🚀**
