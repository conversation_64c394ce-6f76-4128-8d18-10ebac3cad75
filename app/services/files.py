import os
import uuid
from pathlib import Path
from typing import Optional, <PERSON><PERSON>
from PIL import Image
from fastapi import UploadFile

from app.core.config import UPLOAD_DIR, THUMB_MAX_SIDE, MAX_IMAGE_MB, ALLOWED_IMAGE_TYPES

def validate_image(file: UploadFile) -> Tuple[bool, str]:
    """验证图片文件"""
    # 检查文件名是否存在
    if not file.filename:
        return False, "未提供文件"

    # 根据文件扩展名推断 MIME 类型（如果 content_type 为空）
    content_type = file.content_type
    if not content_type:
        filename_lower = file.filename.lower()
        if filename_lower.endswith(('.jpg', '.jpeg')):
            content_type = 'image/jpeg'
        elif filename_lower.endswith('.png'):
            content_type = 'image/png'
        else:
            return False, f"不支持的文件扩展名: {file.filename}"

    # 检查 MIME 类型
    if content_type not in ALLOWED_IMAGE_TYPES:
        return False, f"不支持的图片类型: {content_type}"

    # 检查文件大小
    if hasattr(file, 'size') and file.size:
        if file.size > MAX_IMAGE_MB * 1024 * 1024:
            return False, f"图片大小超过限制: {file.size / (1024*1024):.1f}MB > {MAX_IMAGE_MB}MB"

    return True, ""

def get_file_extension(content_type: str) -> str:
    """根据 MIME 类型获取文件扩展名"""
    if content_type == "image/jpeg":
        return ".jpg"
    elif content_type == "image/png":
        return ".png"
    else:
        return ".jpg"  # 默认

def create_upload_directory(class_id: int, question_id: int, student_no: str) -> Path:
    """创建上传目录"""
    upload_path = Path(UPLOAD_DIR) / f"class_{class_id}" / f"question_{question_id}" / f"student_{student_no}"
    upload_path.mkdir(parents=True, exist_ok=True)
    return upload_path

def save_image_with_thumbnail(
    file: UploadFile,
    class_id: int,
    question_id: int,
    student_no: str
) -> Tuple[str, str, int]:
    """
    保存图片和缩略图
    返回: (原图相对路径, 缩略图相对路径, 文件大小)
    """
    # 创建目录
    upload_dir = create_upload_directory(class_id, question_id, student_no)

    # 确定 content_type
    content_type = file.content_type
    if not content_type and file.filename:
        filename_lower = file.filename.lower()
        if filename_lower.endswith(('.jpg', '.jpeg')):
            content_type = 'image/jpeg'
        elif filename_lower.endswith('.png'):
            content_type = 'image/png'
        else:
            content_type = 'image/jpeg'  # 默认

    # 生成文件名
    file_ext = get_file_extension(content_type)
    filename = f"image_{uuid.uuid4().hex[:8]}{file_ext}"
    thumb_filename = f"image_{uuid.uuid4().hex[:8]}_thumb.jpg"
    
    # 保存原图
    image_path = upload_dir / filename
    thumb_path = upload_dir / thumb_filename
    
    # 读取文件内容
    file_content = file.file.read()
    file_size = len(file_content)
    
    # 保存原图
    with open(image_path, "wb") as f:
        f.write(file_content)
    
    # 生成缩略图
    try:
        with Image.open(image_path) as img:
            # 移除 EXIF 数据
            if hasattr(img, '_getexif'):
                img = img.copy()
            
            # 计算缩略图尺寸
            width, height = img.size
            if width > height:
                new_width = min(width, THUMB_MAX_SIDE)
                new_height = int(height * new_width / width)
            else:
                new_height = min(height, THUMB_MAX_SIDE)
                new_width = int(width * new_height / height)
            
            # 生成缩略图
            img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为 RGB（如果是 PNG 带透明度）
            if img_resized.mode in ("RGBA", "P"):
                img_resized = img_resized.convert("RGB")
            
            # 保存缩略图
            img_resized.save(thumb_path, "JPEG", quality=80, optimize=True)
    
    except Exception as e:
        # 如果缩略图生成失败，删除原图并抛出异常
        if image_path.exists():
            image_path.unlink()
        raise Exception(f"缩略图生成失败: {str(e)}")
    
    # 返回相对路径
    rel_image_path = str(image_path.relative_to(Path(UPLOAD_DIR)))
    rel_thumb_path = str(thumb_path.relative_to(Path(UPLOAD_DIR)))
    
    return rel_image_path, rel_thumb_path, file_size

def delete_old_files(image_path: Optional[str], thumb_path: Optional[str]):
    """删除旧文件"""
    if image_path:
        full_image_path = Path(UPLOAD_DIR) / image_path
        if full_image_path.exists():
            full_image_path.unlink()
    
    if thumb_path:
        full_thumb_path = Path(UPLOAD_DIR) / thumb_path
        if full_thumb_path.exists():
            full_thumb_path.unlink()

def get_image_url(path: Optional[str]) -> Optional[str]:
    """获取图片 URL"""
    if not path:
        return None
    return f"/uploads/{path}"
