import json
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models import Class, Question, Submission, Student, QuestionPublication
from app.services.files import delete_old_files

def get_active_class(db: Session) -> Class:
    """获取当前激活的班级（如果有多个激活班级，返回第一个）"""
    active_class = db.query(Class).filter(Class.is_active == True).order_by(Class.id).first()
    if not active_class:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有激活的班级，请联系管理员"
        )
    return active_class

def get_class_by_code(db: Session, class_code: str) -> Class:
    """根据班级代码获取班级"""
    class_obj = db.query(Class).filter(Class.code == class_code).first()
    if not class_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"班级代码 '{class_code}' 不存在"
        )
    return class_obj

def validate_question_for_submission(db: Session, question_id: int, class_id: int) -> Question:
    """验证题目是否可以提交"""
    question = db.query(Question).filter(Question.id == question_id).first()

    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目不存在"
        )

    # 检查题目是否发布到指定班级
    publication = db.query(QuestionPublication).filter(
        QuestionPublication.question_id == question_id,
        QuestionPublication.class_id == class_id,
        QuestionPublication.is_active == True
    ).first()

    # 如果没有发布记录，检查旧的发布方式（兼容性）
    if not publication:
        # 检查题目是否属于该班级（旧方式）
        if question.class_id == class_id and question.is_open:
            return question

        # 检查题目是否通过published_classes发布到该班级（旧方式）
        if question.is_published and question.published_classes:
            try:
                import json
                published_class_ids = json.loads(question.published_classes)
                if class_id in published_class_ids and question.is_open:
                    return question
            except:
                pass

        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="题目未发布到指定班级或已关闭"
        )

    # 检查题目是否开放提交
    if not question.is_open:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该题目已关闭，不接受提交"
        )

    return question

def parse_numeric_array(numeric_str: Optional[str]) -> List[float]:
    """解析数字数组"""
    if not numeric_str or numeric_str.strip() == "":
        return []
    
    try:
        numeric_data = json.loads(numeric_str)
        if not isinstance(numeric_data, list):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="numeric 必须是数组格式"
            )
        
        # 验证数组元素都是数字
        for item in numeric_data:
            if not isinstance(item, (int, float)):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="numeric 数组中的元素必须都是数字"
                )
        
        return [float(x) for x in numeric_data]
    
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="numeric 不是有效的 JSON 格式"
        )

def find_student_by_no(db: Session, student_no: str, class_id: int) -> Optional[Student]:
    """根据学号和班级ID查找学生"""
    return db.query(Student).filter(
        Student.student_no == student_no,
        Student.class_id == class_id
    ).first()

def get_or_create_submission(
    db: Session,
    question_id: int,
    student_no: str,
    class_id: int,
    student_name: Optional[str] = None,
    note: Optional[str] = None,
    runtime: Optional[float] = None
) -> Submission:
    """获取或创建提交记录"""
    # 尝试从学生表中查找学生信息
    student = find_student_by_no(db, student_no, class_id)
    if student:
        # 如果找到学生记录，使用数据库中的姓名
        actual_student_name = student.name
        student_id = student.id
    else:
        # 如果没有找到学生记录，使用传入的姓名或默认值
        actual_student_name = student_name or f"学生_{student_no}"
        student_id = None

    # 查找现有提交
    existing = db.query(Submission).filter(
        Submission.question_id == question_id,
        Submission.student_no == student_no
    ).first()

    if existing:
        # 更新现有记录
        existing.student_name = actual_student_name
        existing.student_id = student_id
        if note is not None:
            existing.note = note
        if runtime is not None:
            existing.runtime = runtime
        return existing
    else:
        # 创建新记录
        new_submission = Submission(
            class_id=class_id,
            question_id=question_id,
            student_id=student_id,
            student_no=student_no,
            student_name=actual_student_name,
            note=note,
            runtime=runtime
        )
        db.add(new_submission)
        db.flush()  # 获取 ID
        return new_submission

def update_submission_files(
    submission: Submission,
    image_path: Optional[str] = None,
    thumb_path: Optional[str] = None,
    mime: Optional[str] = None,
    size: Optional[int] = None
):
    """更新提交的文件信息"""
    # 如果有新图片，删除旧图片
    if image_path and (submission.image_path or submission.thumb_path):
        delete_old_files(submission.image_path, submission.thumb_path)
    
    # 更新文件信息
    if image_path is not None:
        submission.image_path = image_path
    if thumb_path is not None:
        submission.thumb_path = thumb_path
    if mime is not None:
        submission.mime = mime
    if size is not None:
        submission.size = size

def update_submission_numeric(submission: Submission, numeric_data: List[float]):
    """更新提交的数字数据"""
    if numeric_data:
        submission.numeric_json = json.dumps(numeric_data)
    else:
        submission.numeric_json = None

def mask_student_no(student_no: str, anonymous: bool = True) -> str:
    """遮蔽学号"""
    if not anonymous or len(student_no) <= 3:
        return student_no
    
    # 保留末尾3位
    return "*" * (len(student_no) - 3) + student_no[-3:]
