from datetime import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.session import Base

class AdminUser(Base):
    __tablename__ = "admin_users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    password_hash = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

class Class(Base):
    __tablename__ = "classes"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    code = Column(String, unique=True, nullable=True, index=True)
    is_active = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    questions = relationship("Question", back_populates="class_", cascade="all, delete-orphan")
    submissions = relationship("Submission", back_populates="class_", cascade="all, delete-orphan")
    students = relationship("Student", back_populates="class_", cascade="all, delete-orphan")

class Student(Base):
    __tablename__ = "students"

    id = Column(Integer, primary_key=True, index=True)
    student_no = Column(String, nullable=False, index=True)
    name = Column(String, nullable=False)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=False)
    email = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    class_ = relationship("Class", back_populates="students")
    submissions = relationship("Submission", back_populates="student", cascade="all, delete-orphan")

    # 唯一约束：同一班级内学号唯一
    __table_args__ = (
        UniqueConstraint('class_id', 'student_no', name='uq_student_class_no'),
    )

class Question(Base):
    __tablename__ = "questions"

    id = Column(Integer, primary_key=True, index=True)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=True)  # 修改为可选，创建时不需要指定班级
    title = Column(String, nullable=False)
    description = Column(String, nullable=True)
    question_type = Column(String, default="submit_result")  # 题目类型：submit_program, submit_data, submit_result
    submission_type = Column(String, default="image_data")  # 提交类型：image_data, image_only, data_only, text
    order = Column(Integer, default=0)
    is_published = Column(Boolean, default=False)  # 是否发布给学生（保留兼容性）
    is_open = Column(Boolean, default=False)  # 是否开放提交，创建题目模板时默认不开放
    published_classes = Column(String, nullable=True)  # JSON格式存储发布的班级ID列表（保留兼容性）
    due_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    class_ = relationship("Class", back_populates="questions")
    submissions = relationship("Submission", back_populates="question", cascade="all, delete-orphan")
    publications = relationship("QuestionPublication", back_populates="question", cascade="all, delete-orphan")

class QuestionPublication(Base):
    """题目发布表 - 记录题目发布到班级的关系"""
    __tablename__ = "question_publications"

    id = Column(Integer, primary_key=True, index=True)
    question_id = Column(Integer, ForeignKey("questions.id"), nullable=False)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=False)
    is_active = Column(Boolean, default=True)  # 是否激活（用于开启/关闭题目）
    published_at = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    question = relationship("Question", back_populates="publications")
    class_ = relationship("Class")

    # 唯一约束：同一题目在同一班级只能有一条发布记录
    __table_args__ = (
        UniqueConstraint('question_id', 'class_id', name='uq_question_publication'),
    )

class Submission(Base):
    __tablename__ = "submissions"

    id = Column(Integer, primary_key=True, index=True)
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=False)
    question_id = Column(Integer, ForeignKey("questions.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("students.id"), nullable=True)  # 新增：关联学生表
    student_no = Column(String, nullable=False, index=True)  # 保留：向后兼容
    student_name = Column(String, nullable=True)  # 保留：向后兼容
    note = Column(String, nullable=True)
    numeric_json = Column(String, nullable=True)  # JSON 数组字符串
    text_content = Column(String, nullable=True)  # 新增：文本内容
    image_path = Column(String, nullable=True)
    thumb_path = Column(String, nullable=True)
    mime = Column(String, nullable=True)
    size = Column(Integer, nullable=True)  # 字节
    score = Column(Float, nullable=True)
    comment = Column(String, nullable=True)
    runtime = Column(Float, nullable=True)  # 运行时间（秒）
    visible = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    class_ = relationship("Class", back_populates="submissions")
    question = relationship("Question", back_populates="submissions")
    student = relationship("Student", back_populates="submissions")

    # 唯一约束：同一题目每个学生只能有一条提交
    __table_args__ = (
        UniqueConstraint('question_id', 'student_no', name='uq_submission_question_student'),
    )
