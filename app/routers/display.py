import json
from typing import Optional
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models import Submission, Question, Class
from app.services.files import get_image_url

router = APIRouter(prefix="/display", tags=["display"])

@router.get("/submissions")
def get_display_submissions(
    class_id: int = Query(..., description="班级ID"),
    question_id: int = Query(..., description="题目ID"),
    db: Session = Depends(get_db)
):
    """获取课堂展示的提交数据"""
    # 验证班级和题目是否存在
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")
    
    # 获取该班级该题目的所有可见提交
    submissions = db.query(Submission).filter(
        Submission.class_id == class_id,
        Submission.question_id == question_id,
        Submission.visible == True  # 只显示可见的提交
    ).order_by(Submission.created_at.desc()).all()
    
    # 构建返回数据
    submission_list = []
    for sub in submissions:
        # 解析数字数组
        numeric_data = None
        if sub.numeric_json:
            try:
                numeric_data = json.loads(sub.numeric_json)
            except:
                pass
        
        submission_data = {
            "id": sub.id,
            "student_no": sub.student_no,
            "student_name": sub.student_name or f"学生_{sub.student_no}",
            "note": sub.note,
            "numeric": numeric_data,
            "image_url": get_image_url(sub.image_path),
            "thumb_url": get_image_url(sub.thumb_path),
            "text_data": sub.text_content,  # 课堂展示页面期望的字段名
            "score": sub.score,
            "runtime": sub.runtime,
            "created_at": sub.created_at,
            "updated_at": sub.updated_at
        }
        submission_list.append(submission_data)
    
    return {
        "submissions": submission_list,
        "question_title": question.title,
        "class_name": class_obj.name,
        "total_count": len(submission_list)
    }
