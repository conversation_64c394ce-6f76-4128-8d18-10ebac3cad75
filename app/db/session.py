from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.core.config import DB_URL

# 创建 SQLAlchemy engine
engine = create_engine(DB_URL, connect_args={"check_same_thread": False})

# 创建 SessionLocal 类
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建 Base 类
Base = declarative_base()

def get_db():
    """数据库会话依赖项"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
