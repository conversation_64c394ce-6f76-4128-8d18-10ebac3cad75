from sqlalchemy.orm import Session
from app.db.session import engine, SessionLocal, Base
from app.models import AdminUser, Student
from app.core.security import get_password_hash
from app.core.config import ADMIN_INIT_PASSWORD, ensure_directories

def create_tables():
    """创建所有表"""
    Base.metadata.create_all(bind=engine)

def create_default_admin(db: Session):
    """创建默认管理员"""
    # 检查是否已存在管理员
    existing_admin = db.query(AdminUser).first()
    if existing_admin:
        print("管理员已存在，跳过创建")
        return
    
    # 创建默认管理员
    admin = AdminUser(
        username="admin",
        password_hash=get_password_hash(ADMIN_INIT_PASSWORD)
    )
    
    db.add(admin)
    db.commit()
    
    print(f"默认管理员已创建:")
    print(f"用户名: admin")
    print(f"密码: {ADMIN_INIT_PASSWORD}")
    print("请及时修改默认密码！")

def init_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    
    # 确保目录存在
    ensure_directories()
    
    # 创建表
    create_tables()
    print("数据库表创建完成")
    
    # 创建默认管理员
    db = SessionLocal()
    try:
        create_default_admin(db)
    finally:
        db.close()
    
    print("数据库初始化完成")

if __name__ == "__main__":
    init_database()
