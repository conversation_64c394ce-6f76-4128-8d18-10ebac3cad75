import os
from pathlib import Path

# 数据库配置
DB_URL = "sqlite:///./data/app.db"

# 文件上传配置
UPLOAD_DIR = "./uploads"
MAX_IMAGE_MB = 8
THUMB_MAX_SIDE = 480

# JWT 配置
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production-please-make-it-very-long-and-random")
ACCESS_TOKEN_EXPIRE_MINUTES = 12 * 60  # 12 hours

# 管理员初始密码
ADMIN_INIT_PASSWORD = os.getenv("ADMIN_INIT_PASSWORD", "admin123")

# 支持的图片类型
ALLOWED_IMAGE_TYPES = {"image/jpeg", "image/png"}

# 确保目录存在
def ensure_directories():
    """确保必要的目录存在"""
    Path("./data").mkdir(exist_ok=True)
    Path(UPLOAD_DIR).mkdir(exist_ok=True)
