services:
  student-submission-system:
    build: .
    container_name: student-submission-system
    ports:
      - "30200:30200"
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
    environment:
      - SECRET_KEY=your-production-secret-key-change-this-to-a-very-long-random-string
      - ADMIN_INIT_PASSWORD=admin123
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:30200/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
