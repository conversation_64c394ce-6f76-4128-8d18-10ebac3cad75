#!/usr/bin/env python3
"""
数据库清理脚本
用于清理测试数据，重置数据库到初始状态
"""

import os
import sys
from sqlalchemy.orm import Session

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal, engine
from app.models import (
    AdminUser, Class, Student, Question, QuestionPublication, Submission
)

def clear_all_data():
    """清理所有数据"""
    db = SessionLocal()
    try:
        print("🗑️  开始清理数据库...")
        
        # 删除提交记录
        submission_count = db.query(Submission).count()
        db.query(Submission).delete()
        print(f"   删除 {submission_count} 条提交记录")
        
        # 删除题目发布记录
        publication_count = db.query(QuestionPublication).count()
        db.query(QuestionPublication).delete()
        print(f"   删除 {publication_count} 条题目发布记录")
        
        # 删除题目
        question_count = db.query(Question).count()
        db.query(Question).delete()
        print(f"   删除 {question_count} 条题目记录")
        
        # 删除学生
        student_count = db.query(Student).count()
        db.query(Student).delete()
        print(f"   删除 {student_count} 条学生记录")
        
        # 删除班级
        class_count = db.query(Class).count()
        db.query(Class).delete()
        print(f"   删除 {class_count} 条班级记录")
        
        # 提交更改
        db.commit()
        print("✅ 数据库清理完成！")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 清理失败: {e}")
        raise
    finally:
        db.close()

def clear_submissions_only():
    """只清理提交记录"""
    db = SessionLocal()
    try:
        print("🗑️  开始清理提交记录...")
        
        submission_count = db.query(Submission).count()
        db.query(Submission).delete()
        print(f"   删除 {submission_count} 条提交记录")
        
        db.commit()
        print("✅ 提交记录清理完成！")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 清理失败: {e}")
        raise
    finally:
        db.close()

def clear_questions_and_publications():
    """清理题目和发布记录"""
    db = SessionLocal()
    try:
        print("🗑️  开始清理题目和发布记录...")
        
        # 删除提交记录（因为外键约束）
        submission_count = db.query(Submission).count()
        db.query(Submission).delete()
        print(f"   删除 {submission_count} 条提交记录")
        
        # 删除题目发布记录
        publication_count = db.query(QuestionPublication).count()
        db.query(QuestionPublication).delete()
        print(f"   删除 {publication_count} 条题目发布记录")
        
        # 删除题目
        question_count = db.query(Question).count()
        db.query(Question).delete()
        print(f"   删除 {question_count} 条题目记录")
        
        db.commit()
        print("✅ 题目和发布记录清理完成！")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 清理失败: {e}")
        raise
    finally:
        db.close()

def show_database_status():
    """显示数据库状态"""
    db = SessionLocal()
    try:
        print("📊 数据库状态:")
        print(f"   管理员用户: {db.query(AdminUser).count()} 个")
        print(f"   班级: {db.query(Class).count()} 个")
        print(f"   学生: {db.query(Student).count()} 个")
        print(f"   题目: {db.query(Question).count()} 个")
        print(f"   题目发布记录: {db.query(QuestionPublication).count()} 个")
        print(f"   提交记录: {db.query(Submission).count()} 个")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")
    finally:
        db.close()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python clear_database.py status          # 查看数据库状态")
        print("  python clear_database.py submissions     # 只清理提交记录")
        print("  python clear_database.py questions       # 清理题目和发布记录")
        print("  python clear_database.py all             # 清理所有数据")
        return
    
    command = sys.argv[1].lower()
    
    if command == "status":
        show_database_status()
    elif command == "submissions":
        if input("确定要清理所有提交记录吗？(y/N): ").lower() == 'y':
            clear_submissions_only()
        else:
            print("操作已取消")
    elif command == "questions":
        if input("确定要清理题目和发布记录吗？(y/N): ").lower() == 'y':
            clear_questions_and_publications()
        else:
            print("操作已取消")
    elif command == "all":
        if input("确定要清理所有数据吗？这将删除除管理员外的所有数据！(y/N): ").lower() == 'y':
            clear_all_data()
        else:
            print("操作已取消")
    else:
        print(f"未知命令: {command}")
        print("可用命令: status, submissions, questions, all")

if __name__ == "__main__":
    main()
