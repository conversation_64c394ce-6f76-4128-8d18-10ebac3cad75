<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生作业提交系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/styles.css">
</head>
<body>
    <div id="app"></div>
    
    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        
        const SubmitApp = {
            setup() {
                const student = ref(null);
                const questions = ref([]);
                const selectedQuestion = ref(null);
                const loading = ref(true);
                const submitting = ref(false);
                
                const form = reactive({
                    student_id: '',
                    name: '',
                    image_data: null,
                    text_data: ''
                });

                const loadQuestions = async () => {
                    try {
                        const response = await fetch('/student/questions');
                        if (response.ok) {
                            questions.value = await response.json();
                        }
                    } catch (error) {
                        console.error('加载题目失败:', error);
                    } finally {
                        loading.value = false;
                    }
                };

                const handleFileChange = (event) => {
                    const file = event.target.files[0];
                    if (file) {
                        form.image_data = file;
                    }
                };

                const handleSubmit = async () => {
                    if (!form.student_id || !form.name || !selectedQuestion.value) {
                        alert('请填写完整信息并选择题目');
                        return;
                    }

                    submitting.value = true;
                    
                    try {
                        const formData = new FormData();
                        formData.append('student_id', form.student_id);
                        formData.append('name', form.name);
                        formData.append('question_id', selectedQuestion.value.id);
                        
                        if (form.image_data) {
                            formData.append('image_data', form.image_data);
                        }
                        if (form.text_data) {
                            formData.append('text_data', form.text_data);
                        }

                        const response = await fetch('/student/submit', {
                            method: 'POST',
                            body: formData
                        });

                        if (response.ok) {
                            alert('提交成功！');
                            // 重置表单
                            form.image_data = null;
                            form.text_data = '';
                            document.getElementById('fileInput').value = '';
                        } else {
                            const error = await response.json();
                            alert('提交失败: ' + (error.detail || '未知错误'));
                        }
                    } catch (error) {
                        alert('提交失败: ' + error.message);
                    } finally {
                        submitting.value = false;
                    }
                };

                onMounted(loadQuestions);

                return {
                    student,
                    questions,
                    selectedQuestion,
                    loading,
                    submitting,
                    form,
                    handleFileChange,
                    handleSubmit
                };
            },
            template: `
                <div class="app-container">
                    <header class="header">
                        <h1>
                            <i class="fas fa-upload"></i> 
                            学生作业提交系统
                        </h1>
                    </header>
                    
                    <main class="main-content">
                        <div class="content-card">
                            <h2 style="text-align: center; margin-bottom: 2rem;">
                                <i class="fas fa-file-upload"></i> 
                                作业提交
                            </h2>
                            
                            <div v-if="loading" class="loading">
                                <div class="spinner"></div>
                            </div>
                            
                            <form v-else @submit.prevent="handleSubmit">
                                <div class="grid">
                                    <div class="form-group">
                                        <label>学号 *</label>
                                        <input 
                                            type="text" 
                                            v-model="form.student_id" 
                                            class="form-control" 
                                            required
                                            placeholder="请输入学号"
                                        >
                                    </div>
                                    <div class="form-group">
                                        <label>姓名 *</label>
                                        <input 
                                            type="text" 
                                            v-model="form.name" 
                                            class="form-control" 
                                            required
                                            placeholder="请输入姓名"
                                        >
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>选择题目 *</label>
                                    <select v-model="selectedQuestion" class="form-control" required>
                                        <option :value="null">请选择题目</option>
                                        <option v-for="q in questions" :key="q.id" :value="q">
                                            {{ q.title }}
                                        </option>
                                    </select>
                                </div>
                                
                                <div v-if="selectedQuestion" class="form-group">
                                    <label>题目描述</label>
                                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px; border-left: 4px solid #667eea;">
                                        {{ selectedQuestion.description || '暂无描述' }}
                                    </div>
                                </div>
                                
                                <div v-if="selectedQuestion && (selectedQuestion.submission_type === 'image_data' || selectedQuestion.submission_type === 'image_only')" class="form-group">
                                    <label>上传图片</label>
                                    <input 
                                        type="file" 
                                        id="fileInput"
                                        @change="handleFileChange" 
                                        class="form-control" 
                                        accept="image/*"
                                    >
                                </div>
                                
                                <div v-if="selectedQuestion && (selectedQuestion.submission_type === 'image_data' || selectedQuestion.submission_type === 'data_only' || selectedQuestion.submission_type === 'text')" class="form-group">
                                    <label>文本内容</label>
                                    <textarea 
                                        v-model="form.text_data" 
                                        class="form-control" 
                                        rows="5"
                                        placeholder="请输入文本内容或数据"
                                    ></textarea>
                                </div>
                                
                                <button 
                                    type="submit" 
                                    class="btn btn-primary" 
                                    style="width: 100%"
                                    :disabled="submitting"
                                >
                                    <i :class="submitting ? 'fas fa-spinner fa-spin' : 'fas fa-upload'"></i>
                                    {{ submitting ? '提交中...' : '提交作业' }}
                                </button>
                            </form>
                        </div>
                    </main>
                </div>
            `
        };
        
        createApp(SubmitApp).mount('#app');
    </script>
</body>
</html>
