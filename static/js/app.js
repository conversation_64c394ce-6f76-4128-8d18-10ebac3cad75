// 主应用
const { createApp, ref, computed, onMounted } = Vue;

const App = {
    setup() {
        const { isLoggedIn, checkAuth, logout, user } = useAuth();
        const activeTab = ref('dashboard');
        
        const tabs = [
            { id: 'dashboard', name: '仪表板', icon: 'fas fa-chart-bar', component: 'Dashboard' },
            { id: 'classes', name: '班级管理', icon: 'fas fa-school', component: 'ClassManagement' },
            { id: 'students', name: '学生管理', icon: 'fas fa-users', component: 'StudentManagement' },
            { id: 'questions', name: '题目管理', icon: 'fas fa-question-circle', component: 'QuestionManagement' },
            { id: 'publications', name: '发布管理', icon: 'fas fa-broadcast-tower', component: 'PublicationManagement' },
            { id: 'submissions', name: '提交管理', icon: 'fas fa-file-upload', component: 'SubmissionManagement' }
        ];

        const currentComponent = computed(() => {
            const tab = tabs.find(t => t.id === activeTab.value);
            return tab ? tab.component : 'Dashboard';
        });

        const setActiveTab = (tabId) => {
            activeTab.value = tabId;
        };

        onMounted(async () => {
            if (isLoggedIn.value) {
                await checkAuth();
            }
        });

        return {
            isLoggedIn,
            activeTab,
            tabs,
            currentComponent,
            setActiveTab,
            logout,
            user
        };
    },
    template: `
        <div class="app-container">
            <NotificationContainer />
            
            <template v-if="!isLoggedIn">
                <LoginForm />
            </template>
            
            <template v-else>
                <header class="header">
                    <h1>
                        <i class="fas fa-graduation-cap"></i> 
                        学生作业提交系统
                    </h1>
                    <div class="user-info">
                        <span>
                            <i class="fas fa-user"></i> 
                            管理员已登录
                        </span>
                        <button class="btn btn-danger" @click="logout">
                            <i class="fas fa-sign-out-alt"></i> 
                            退出
                        </button>
                    </div>
                </header>
                
                <main class="main-content">
                    <nav class="nav-tabs">
                        <div 
                            v-for="tab in tabs" 
                            :key="tab.id"
                            :class="['nav-tab', { active: activeTab === tab.id }]"
                            @click="setActiveTab(tab.id)"
                        >
                            <i :class="tab.icon"></i> 
                            {{ tab.name }}
                        </div>
                    </nav>
                    
                    <transition name="fade" mode="out-in">
                        <component :is="currentComponent" :key="activeTab" />
                    </transition>
                </main>
            </template>
        </div>
    `,
    components: {
        NotificationContainer,
        LoginForm,
        Dashboard,
        ClassManagement,
        StudentManagement,
        QuestionManagement,
        PublicationManagement,
        SubmissionManagement
    }
};

// 创建并挂载应用
createApp(App).mount('#app');
