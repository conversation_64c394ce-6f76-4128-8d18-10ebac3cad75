// 学生管理组件
const StudentManagement = {
    setup() {
        const { ref, onMounted } = Vue;
        const { get, post, put, del } = useApi();
        const { success, error } = useNotification();
        
        const students = ref([]);
        const classes = ref([]);
        const selectedClass = ref('');
        const loading = ref(true);
        const showModal = ref(false);
        const editingStudent = ref(null);
        const form = ref({
            student_no: '',
            name: '',
            class_id: '',
            email: ''
        });

        const loadClasses = async () => {
            try {
                classes.value = await get('/admin/classes');
            } catch (err) {
                console.error('加载班级列表失败:', err);
            }
        };

        const loadStudents = async () => {
            try {
                const params = selectedClass.value ? { class_id: selectedClass.value } : {};
                students.value = await get('/admin/students', params);
            } catch (err) {
                error('加载学生列表失败');
            } finally {
                loading.value = false;
            }
        };

        const handleClassChange = () => {
            loading.value = true;
            loadStudents();
        };

        const openCreateModal = () => {
            editingStudent.value = null;
            form.value = {
                student_no: '',
                name: '',
                class_id: classes.value[0]?.id || '',
                email: ''
            };
            showModal.value = true;
        };

        const openEditModal = (student) => {
            editingStudent.value = student;
            form.value = { ...student };
            showModal.value = true;
        };

        const closeModal = () => {
            showModal.value = false;
            editingStudent.value = null;
            form.value = {
                student_no: '',
                name: '',
                class_id: '',
                email: ''
            };
        };

        const handleSubmit = async () => {
            if (!form.value.student_no.trim() || !form.value.name.trim() || !form.value.class_id) {
                error('请填写完整信息');
                return;
            }

            try {
                if (editingStudent.value) {
                    // 编辑学生
                    await put(`/admin/students/${editingStudent.value.id}`, form.value);
                    success('学生信息更新成功');
                } else {
                    // 创建学生
                    await post('/admin/students', form.value);
                    success('学生创建成功');
                }

                closeModal();
                await loadStudents();
            } catch (err) {
                error(editingStudent.value ? '学生信息更新失败' : '学生创建失败');
            }
        };

        const handleDelete = async (student) => {
            if (!confirm(`确定要删除学生"${student.name}"吗？`)) {
                return;
            }

            try {
                await del(`/admin/students/${student.id}`);
                success('学生删除成功');
                await loadStudents();
            } catch (err) {
                error('学生删除失败');
            }
        };

        onMounted(async () => {
            await loadClasses();
            await loadStudents();
        });

        return {
            students,
            classes,
            selectedClass,
            loading,
            showModal,
            form,
            editingStudent,
            handleClassChange,
            openCreateModal,
            openEditModal,
            closeModal,
            handleSubmit,
            handleDelete,
            formatDateTime: helpers.formatDateTime
        };
    },
    template: `
        <div class="content-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2>
                    <i class="fas fa-users"></i> 
                    学生管理
                </h2>
                <div>
                    <button class="btn btn-primary" @click="openCreateModal">
                        <i class="fas fa-user-plus"></i>
                        添加学生
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-file-import"></i>
                        批量导入
                    </button>
                </div>
            </div>
            
            <div class="form-group" style="max-width: 300px; margin-bottom: 2rem;">
                <label>选择班级</label>
                <select v-model="selectedClass" @change="handleClassChange" class="form-control">
                    <option value="">所有班级</option>
                    <option v-for="cls in classes" :key="cls.id" :value="cls.id">
                        {{ cls.name }}
                    </option>
                </select>
            </div>
            
            <div v-if="loading" class="loading">
                <div class="spinner"></div>
            </div>
            
            <div v-else class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>学号</th>
                            <th>姓名</th>
                            <th>班级</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="student in students" :key="student.id">
                            <td>{{ student.id }}</td>
                            <td>{{ student.student_no }}</td>
                            <td>{{ student.name }}</td>
                            <td>{{ student.class_name }}</td>
                            <td>
                                <span :class="['status-badge', student.is_active ? 'status-active' : 'status-inactive']">
                                    {{ student.is_active ? '激活' : '未激活' }}
                                </span>
                            </td>
                            <td>{{ formatDateTime(student.created_at) }}</td>
                            <td>
                                <button class="btn btn-outline" @click="openEditModal(student)">
                                    <i class="fas fa-edit"></i>
                                    编辑
                                </button>
                                <button class="btn btn-danger" @click="handleDelete(student)">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div v-if="students.length === 0" style="text-align: center; padding: 2rem; color: #666;">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>{{ selectedClass ? '该班级暂无学生' : '暂无学生数据' }}</p>
                </div>
            </div>

            <!-- 模态框 -->
            <div v-if="showModal" class="modal-overlay" @click="closeModal">
                <div class="modal-content" @click.stop>
                    <h3>{{ editingStudent ? '编辑学生' : '添加学生' }}</h3>
                    <form @submit.prevent="handleSubmit">
                        <div class="form-group">
                            <label>学号 *</label>
                            <input
                                type="text"
                                v-model="form.student_no"
                                class="form-control"
                                required
                                placeholder="请输入学号"
                            >
                        </div>
                        <div class="form-group">
                            <label>姓名 *</label>
                            <input
                                type="text"
                                v-model="form.name"
                                class="form-control"
                                required
                                placeholder="请输入姓名"
                            >
                        </div>
                        <div class="form-group">
                            <label>班级 *</label>
                            <select v-model="form.class_id" class="form-control" required>
                                <option value="">请选择班级</option>
                                <option v-for="cls in classes" :key="cls.id" :value="cls.id">
                                    {{ cls.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>邮箱</label>
                            <input
                                type="email"
                                v-model="form.email"
                                class="form-control"
                                placeholder="请输入邮箱（可选）"
                            >
                        </div>
                        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                            <button type="button" class="btn btn-outline" @click="closeModal">
                                取消
                            </button>
                            <button type="submit" class="btn btn-primary">
                                {{ editingStudent ? '更新' : '创建' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `
};

// 全局注册
window.StudentManagement = StudentManagement;
