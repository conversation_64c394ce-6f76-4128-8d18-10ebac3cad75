// 题目管理组件
const QuestionManagement = {
    setup() {
        const { ref, onMounted } = Vue;
        const { get, post, put, del } = useApi();
        const { success, error } = useNotification();
        
        const questions = ref([]);
        const classes = ref([]);
        const loading = ref(true);
        const showModal = ref(false);
        const showPublishModal = ref(false);
        const editingQuestion = ref(null);
        const publishingQuestion = ref(null);
        const form = ref({
            title: '',
            description: '',
            submission_type: 'text'
        });
        const publishForm = ref({
            class_ids: []
        });

        const loadQuestions = async () => {
            try {
                questions.value = await get('/admin/questions');
            } catch (err) {
                error('加载题目列表失败');
            } finally {
                loading.value = false;
            }
        };

        const loadClasses = async () => {
            try {
                classes.value = await get('/admin/classes');
            } catch (err) {
                console.error('加载班级列表失败:', err);
            }
        };

        const openCreateModal = () => {
            editingQuestion.value = null;
            form.value = {
                title: '',
                description: '',
                submission_type: 'text'
            };
            showModal.value = true;
        };

        const openEditModal = (question) => {
            editingQuestion.value = question;
            form.value = { ...question };
            showModal.value = true;
        };

        const closeModal = () => {
            showModal.value = false;
            editingQuestion.value = null;
            form.value = {
                title: '',
                description: '',
                submission_type: 'text'
            };
        };

        const handleSubmit = async () => {
            if (!form.value.title.trim() || !form.value.description.trim()) {
                error('请填写完整信息');
                return;
            }

            try {
                if (editingQuestion.value) {
                    // 编辑题目
                    await put(`/admin/questions/${editingQuestion.value.id}`, form.value);
                    success('题目更新成功');
                } else {
                    // 创建题目
                    await post('/admin/questions', form.value);
                    success('题目创建成功');
                }

                closeModal();
                await loadQuestions();
            } catch (err) {
                error(editingQuestion.value ? '题目更新失败' : '题目创建失败');
            }
        };

        const handleDelete = async (question) => {
            if (!confirm(`确定要删除题目"${question.title}"吗？`)) {
                return;
            }

            try {
                await del(`/admin/questions/${question.id}`);
                success('题目删除成功');
                await loadQuestions();
            } catch (err) {
                error('题目删除失败');
            }
        };

        const openPublishModal = (question) => {
            publishingQuestion.value = question;
            publishForm.value = { class_ids: [] };
            showPublishModal.value = true;
        };

        const closePublishModal = () => {
            showPublishModal.value = false;
            publishingQuestion.value = null;
            publishForm.value = { class_ids: [] };
        };

        const handlePublish = async () => {
            if (publishForm.value.class_ids.length === 0) {
                error('请选择要发布的班级');
                return;
            }

            try {
                await post('/admin/question-publications', {
                    question_id: publishingQuestion.value.id,
                    class_ids: publishForm.value.class_ids
                });
                success('题目发布成功');
                closePublishModal();
            } catch (err) {
                error('题目发布失败');
            }
        };

        onMounted(async () => {
            await loadClasses();
            await loadQuestions();
        });

        return {
            questions,
            classes,
            loading,
            showModal,
            showPublishModal,
            form,
            publishForm,
            editingQuestion,
            publishingQuestion,
            openCreateModal,
            openEditModal,
            closeModal,
            handleSubmit,
            handleDelete,
            openPublishModal,
            closePublishModal,
            handlePublish,
            getSubmissionTypeText: helpers.getSubmissionTypeText,
            formatDateTime: helpers.formatDateTime
        };
    },
    template: `
        <div class="content-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2>
                    <i class="fas fa-question-circle"></i> 
                    题目管理（题库）
                </h2>
                <button class="btn btn-primary" @click="openCreateModal">
                    <i class="fas fa-plus"></i>
                    创建题目模板
                </button>
            </div>
            
            <div style="background: rgba(102, 126, 234, 0.1); padding: 1rem; border-radius: 10px; margin-bottom: 2rem;">
                <p style="margin: 0; color: #667eea;">
                    <i class="fas fa-info-circle"></i> 
                    题目模板是可重复使用的题目，不绑定特定班级，可发布到多个班级
                </p>
            </div>
            
            <div v-if="loading" class="loading">
                <div class="spinner"></div>
            </div>
            
            <div v-else class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>题目名称</th>
                            <th>描述</th>
                            <th>提交类型</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="question in questions" :key="question.id">
                            <td>{{ question.id }}</td>
                            <td>{{ question.title }}</td>
                            <td>{{ question.description || '-' }}</td>
                            <td>
                                <span class="status-badge status-active">
                                    {{ getSubmissionTypeText(question.submission_type) }}
                                </span>
                            </td>
                            <td>{{ formatDateTime(question.created_at) }}</td>
                            <td>
                                <button class="btn btn-outline" @click="openEditModal(question)">
                                    <i class="fas fa-edit"></i>
                                    编辑
                                </button>
                                <button class="btn btn-success" @click="openPublishModal(question)">
                                    <i class="fas fa-share"></i>
                                    发布到班级
                                </button>
                                <button class="btn btn-danger" @click="handleDelete(question)">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div v-if="questions.length === 0" style="text-align: center; padding: 2rem; color: #666;">
                    <i class="fas fa-question-circle" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>暂无题目模板，请先创建题目</p>
                </div>
            </div>

            <!-- 题目编辑模态框 -->
            <div v-if="showModal" class="modal-overlay" @click="closeModal">
                <div class="modal-content" @click.stop>
                    <h3>{{ editingQuestion ? '编辑题目' : '创建题目模板' }}</h3>
                    <form @submit.prevent="handleSubmit">
                        <div class="form-group">
                            <label>题目标题 *</label>
                            <input
                                type="text"
                                v-model="form.title"
                                class="form-control"
                                required
                                placeholder="请输入题目标题"
                            >
                        </div>
                        <div class="form-group">
                            <label>题目描述 *</label>
                            <textarea
                                v-model="form.description"
                                class="form-control"
                                rows="4"
                                required
                                placeholder="请输入题目描述"
                            ></textarea>
                        </div>
                        <div class="form-group">
                            <label>提交类型 *</label>
                            <select v-model="form.submission_type" class="form-control" required>
                                <option value="text">文本</option>
                                <option value="image">图片</option>
                                <option value="file">文件</option>
                                <option value="image_data">图片+文本</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                            <button type="button" class="btn btn-outline" @click="closeModal">
                                取消
                            </button>
                            <button type="submit" class="btn btn-primary">
                                {{ editingQuestion ? '更新' : '创建' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 发布模态框 -->
            <div v-if="showPublishModal" class="modal-overlay" @click="closePublishModal">
                <div class="modal-content" @click.stop>
                    <h3>发布题目到班级</h3>
                    <p style="margin-bottom: 1rem; color: #666;">
                        题目：{{ publishingQuestion?.title }}
                    </p>
                    <form @submit.prevent="handlePublish">
                        <div class="form-group">
                            <label>选择班级 *</label>
                            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; padding: 0.5rem;">
                                <label v-for="cls in classes" :key="cls.id" style="display: block; margin-bottom: 0.5rem;">
                                    <input
                                        type="checkbox"
                                        :value="cls.id"
                                        v-model="publishForm.class_ids"
                                        style="margin-right: 0.5rem;"
                                    >
                                    {{ cls.name }}
                                </label>
                            </div>
                        </div>
                        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                            <button type="button" class="btn btn-outline" @click="closePublishModal">
                                取消
                            </button>
                            <button type="submit" class="btn btn-success">
                                发布
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `
};

// 全局注册
window.QuestionManagement = QuestionManagement;
