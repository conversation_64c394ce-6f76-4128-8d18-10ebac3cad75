// 登录表单组件
const LoginForm = {
    setup() {
        const { reactive, ref } = Vue;
        const { login } = useAuth();
        const { success, error } = useNotification();
        
        const form = reactive({
            username: 'admin',
            password: 'admin123'
        });
        
        const loading = ref(false);

        const handleSubmit = async () => {
            if (!form.username || !form.password) {
                error('请填写用户名和密码');
                return;
            }

            loading.value = true;

            try {
                const result = await login(form.username, form.password);

                if (result.success) {
                    success('登录成功！');
                    // 强制触发页面重新渲染
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                } else {
                    error(result.message);
                }
            } catch (err) {
                error('登录过程中发生错误');
            } finally {
                loading.value = false;
            }
        };

        const handleKeyPress = (event) => {
            if (event.key === 'Enter') {
                handleSubmit();
            }
        };

        return { 
            form, 
            loading, 
            handleSubmit, 
            handleKeyPress 
        };
    },
    template: `
        <div class="login-container">
            <div class="login-card">
                <h2>
                    <i class="fas fa-user-shield"></i> 
                    管理员登录
                </h2>
                <form @submit.prevent="handleSubmit">
                    <div class="form-group">
                        <label>
                            <i class="fas fa-user"></i> 
                            用户名
                        </label>
                        <input 
                            type="text" 
                            v-model="form.username" 
                            class="form-control" 
                            required
                            :disabled="loading"
                            @keypress="handleKeyPress"
                            placeholder="请输入用户名"
                        >
                    </div>
                    <div class="form-group">
                        <label>
                            <i class="fas fa-lock"></i> 
                            密码
                        </label>
                        <input 
                            type="password" 
                            v-model="form.password" 
                            class="form-control" 
                            required
                            :disabled="loading"
                            @keypress="handleKeyPress"
                            placeholder="请输入密码"
                        >
                    </div>
                    <button 
                        type="submit" 
                        class="btn btn-primary" 
                        style="width: 100%"
                        :disabled="loading"
                    >
                        <i :class="loading ? 'fas fa-spinner fa-spin' : 'fas fa-sign-in-alt'"></i>
                        {{ loading ? '登录中...' : '登录' }}
                    </button>
                </form>
                <div style="text-align: center; margin-top: 1rem; color: #666; font-size: 0.9rem;">
                    <p>默认账号：admin / admin123</p>
                </div>
            </div>
        </div>
    `
};

// 全局注册
window.LoginForm = LoginForm;
