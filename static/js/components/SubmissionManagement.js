// 提交管理组件
const SubmissionManagement = {
    setup() {
        const { ref, computed, onMounted } = Vue;
        const { get, patch } = useApi();
        const { error, success } = useNotification();
        
        const submissions = ref([]);
        const classes = ref([]);
        const questions = ref([]);
        const selectedClasses = ref([]);
        const selectedQuestion = ref('');
        const loading = ref(true);

        const loadFilters = async () => {
            try {
                const [classesData, questionsData] = await Promise.all([
                    get('/admin/classes'),
                    get('/admin/question-publications')
                ]);
                classes.value = classesData;
                questions.value = questionsData;
            } catch (err) {
                console.error('加载筛选器失败:', err);
            }
        };

        const loadSubmissions = async () => {
            try {
                const params = new URLSearchParams();

                // 为每个班级ID添加单独的参数
                if (selectedClasses.value.length > 0) {
                    selectedClasses.value.forEach(classId => {
                        params.append('class_id', classId);
                    });
                }
                if (selectedQuestion.value) {
                    params.append('question_id', selectedQuestion.value);
                }

                const queryString = params.toString();
                const url = queryString ? `/admin/submissions?${queryString}` : '/admin/submissions';

                // 直接使用fetch而不是get方法，因为我们已经构建了完整的URL
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${useAuth().token.value}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('请求失败');
                }

                submissions.value = await response.json();
            } catch (err) {
                error('加载提交管理失败');
            } finally {
                loading.value = false;
            }
        };

        const canOpenClassroomDisplay = computed(() => {
            return selectedClasses.value.length > 0 && selectedQuestion.value;
        });

        const openClassroomDisplay = () => {
            if (!canOpenClassroomDisplay.value) {
                error('请先选择班级和题目');
                return;
            }
            
            const url = `/static/classroom_display.html?class_id=${selectedClasses.value[0]}&question_id=${selectedQuestion.value}`;
            window.open(url, '_blank', 'fullscreen=yes,scrollbars=yes');
            success('课堂展示窗口已打开');
        };

        const handleFiltersChange = () => {
            loading.value = true;
            loadSubmissions();
        };

        const viewSubmission = (submission) => {
            // 创建查看提交的模态框或新窗口
            const content = [];
            if (submission.text_data) {
                content.push(`文本内容：\n${submission.text_data}`);
            }
            if (submission.image_url) {
                content.push(`图片：${submission.image_url}`);
            }
            if (submission.file_url) {
                content.push(`文件：${submission.file_url}`);
            }

            alert(`学生：${submission.student_name}\n题目：${submission.question_title}\n提交时间：${helpers.formatDateTime(submission.created_at)}\n\n${content.join('\n\n')}`);
        };

        const toggleVisibility = async (submission) => {
            try {
                const newVisibility = !submission.visible;
                await patch(`/admin/submissions/${submission.id}`, {
                    visible: newVisibility
                });
                success(`提交已${newVisibility ? '显示' : '隐藏'}`);
                await loadSubmissions();
            } catch (err) {
                error('状态更新失败');
            }
        };

        onMounted(async () => {
            await loadFilters();
            await loadSubmissions();
        });

        return {
            submissions,
            classes,
            questions,
            selectedClasses,
            selectedQuestion,
            loading,
            canOpenClassroomDisplay,
            openClassroomDisplay,
            handleFiltersChange,
            viewSubmission,
            toggleVisibility,
            formatDateTime: helpers.formatDateTime
        };
    },
    template: `
        <div class="content-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2>
                    <i class="fas fa-file-upload"></i> 
                    提交管理
                </h2>
                <button 
                    class="btn btn-success" 
                    @click="openClassroomDisplay"
                    :disabled="!canOpenClassroomDisplay"
                >
                    <i class="fas fa-desktop"></i> 
                    🖥️ 全屏展示
                </button>
            </div>
            
            <div class="grid" style="margin-bottom: 2rem;">
                <div class="form-group">
                    <label>选择班级（可多选）</label>
                    <select 
                        v-model="selectedClasses" 
                        @change="handleFiltersChange" 
                        class="form-control" 
                        multiple 
                        style="height: 100px;"
                    >
                        <option v-for="cls in classes" :key="cls.id" :value="cls.id">
                            {{ cls.name }}
                        </option>
                    </select>
                    <small style="color: #666;">按住Ctrl键可多选班级</small>
                </div>
                <div class="form-group">
                    <label>选择题目</label>
                    <select v-model="selectedQuestion" @change="handleFiltersChange" class="form-control">
                        <option value="">所有题目</option>
                        <option v-for="q in questions" :key="q.question_id" :value="q.question_id">
                            {{ q.question_title }}
                        </option>
                    </select>
                </div>
            </div>
            
            <div v-if="loading" class="loading">
                <div class="spinner"></div>
            </div>
            
            <div v-else class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>学号</th>
                            <th>姓名</th>
                            <th>题目</th>
                            <th>提交时间</th>
                            <th>分数</th>
                            <th>可见</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="sub in submissions" :key="sub.id">
                            <td>{{ sub.id }}</td>
                            <td>{{ sub.student_no }}</td>
                            <td>{{ sub.student_name }}</td>
                            <td>{{ sub.question_title }}</td>
                            <td>{{ formatDateTime(sub.created_at) }}</td>
                            <td>{{ sub.score || '-' }}</td>
                            <td>
                                <span :class="['status-badge', sub.visible ? 'status-active' : 'status-inactive']">
                                    {{ sub.visible ? '可见' : '隐藏' }}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-outline" @click="viewSubmission(sub)">
                                    <i class="fas fa-eye"></i>
                                    查看
                                </button>
                                <button class="btn btn-outline" @click="toggleVisibility(sub)">
                                    <i :class="sub.visible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                                    {{ sub.visible ? '隐藏' : '显示' }}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div v-if="submissions.length === 0" style="text-align: center; padding: 2rem; color: #666;">
                    <i class="fas fa-file-upload" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>暂无提交数据</p>
                </div>
            </div>
        </div>
    `
};

// 全局注册
window.SubmissionManagement = SubmissionManagement;
