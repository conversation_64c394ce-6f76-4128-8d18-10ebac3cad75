<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课堂展示</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }

        .display-container {
            min-height: 100vh;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .submissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .submission-card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .submission-card:hover {
            transform: translateY(-5px);
        }

        .student-info {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #eee;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
        }

        .submission-content {
            margin-bottom: 1rem;
        }

        .submission-image {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .submission-text {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            white-space: pre-wrap;
            max-height: 150px;
            overflow-y: auto;
        }

        .submission-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 50vh;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 6px solid rgba(255,255,255,0.3);
            border-top: 6px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-submissions {
            text-align: center;
            padding: 4rem;
            color: rgba(255,255,255,0.8);
        }

        .no-submissions i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div id="app"></div>

    <script>
        const { createApp, ref, onMounted } = Vue;

        const ClassroomDisplay = {
            setup() {
                const submissions = ref([]);
                const loading = ref(true);
                const questionTitle = ref('');
                const className = ref('');

                const getUrlParams = () => {
                    const params = new URLSearchParams(window.location.search);
                    return {
                        class_id: params.get('class_id'),
                        question_id: params.get('question_id')
                    };
                };

                const loadSubmissions = async () => {
                    const { class_id, question_id } = getUrlParams();

                    if (!class_id || !question_id) {
                        console.error('缺少必要参数');
                        loading.value = false;
                        return;
                    }

                    try {
                        const response = await fetch(`/display/submissions?class_id=${class_id}&question_id=${question_id}`);
                        if (response.ok) {
                            const data = await response.json();
                            submissions.value = data.submissions || [];
                            questionTitle.value = data.question_title || '未知题目';
                            className.value = data.class_name || '未知班级';
                        }
                    } catch (error) {
                        console.error('加载提交数据失败:', error);
                    } finally {
                        loading.value = false;
                    }
                };

                const formatDateTime = (dateString) => {
                    if (!dateString) return '';
                    return new Date(dateString).toLocaleString('zh-CN');
                };

                const getStudentInitial = (name) => {
                    return name ? name.charAt(0).toUpperCase() : '?';
                };

                // 定期刷新数据
                const startAutoRefresh = () => {
                    setInterval(loadSubmissions, 10000); // 每10秒刷新一次
                };

                onMounted(() => {
                    loadSubmissions();
                    startAutoRefresh();
                });

                return {
                    submissions,
                    loading,
                    questionTitle,
                    className,
                    formatDateTime,
                    getStudentInitial
                };
            },
            template: `
                <div class="display-container">
                    <div class="header">
                        <h1>
                            <i class="fas fa-chalkboard-teacher"></i>
                            课堂展示
                        </h1>
                        <p style="font-size: 1.2rem; opacity: 0.9;">
                            {{ className }} - {{ questionTitle }}
                        </p>
                    </div>

                    <div v-if="loading" class="loading">
                        <div class="spinner"></div>
                    </div>

                    <div v-else-if="submissions.length === 0" class="no-submissions">
                        <i class="fas fa-inbox"></i>
                        <h2>暂无提交</h2>
                        <p>等待学生提交作业...</p>
                    </div>

                    <div v-else class="submissions-grid">
                        <div v-for="submission in submissions" :key="submission.id" class="submission-card">
                            <div class="student-info">
                                <div class="student-avatar">
                                    {{ getStudentInitial(submission.student_name) }}
                                </div>
                                <div>
                                    <div style="font-weight: bold;">{{ submission.student_name }}</div>
                                    <div style="color: #666; font-size: 0.9rem;">{{ submission.student_no }}</div>
                                </div>
                            </div>

                            <div class="submission-content">
                                <div v-if="submission.image_url">
                                    <img :src="submission.image_url" class="submission-image" alt="提交图片">
                                </div>
                                <div v-if="submission.text_data" class="submission-text">
                                    {{ submission.text_data }}
                                </div>
                            </div>

                            <div class="submission-meta">
                                <span>
                                    <i class="fas fa-clock"></i>
                                    {{ formatDateTime(submission.created_at) }}
                                </span>
                                <span v-if="submission.score">
                                    <i class="fas fa-star"></i>
                                    {{ submission.score }}分
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            `
        };

        createApp(ClassroomDisplay).mount('#app');
    </script>
</body>
</html>