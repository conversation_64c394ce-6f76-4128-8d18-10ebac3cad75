#!/usr/bin/env python3
"""
正弦曲线测试脚本
生成正弦曲线图像和数据，然后提交到系统进行测试
"""

import numpy as np
import matplotlib.pyplot as plt
import requests
import json
import io
import os
from PIL import Image
import tempfile

# 配置
BASE_URL = "http://localhost:30200"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def generate_sine_wave_data():
    """生成正弦曲线数据"""
    print("📊 生成正弦曲线数据...")
    
    # 生成x轴数据 (0到4π，100个点)
    x = np.linspace(0, 4*np.pi, 100)
    
    # 生成不同频率和振幅的正弦波
    y1 = np.sin(x)                    # 基础正弦波
    y2 = 0.5 * np.sin(2*x)           # 高频低振幅
    y3 = 1.5 * np.sin(0.5*x)         # 低频高振幅
    y_combined = y1 + y2 + y3         # 复合波形
    
    return x, y1, y2, y3, y_combined

def create_sine_wave_plot(x, y1, y2, y3, y_combined):
    """创建正弦曲线图像"""
    print("🎨 创建正弦曲线图像...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图像
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Sine Wave Analysis - Physics Experiment', fontsize=16, fontweight='bold')
    
    # 子图1: 基础正弦波
    ax1.plot(x, y1, 'b-', linewidth=2, label='sin(x)')
    ax1.set_title('Basic Sine Wave')
    ax1.set_xlabel('x (radians)')
    ax1.set_ylabel('Amplitude')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 子图2: 高频正弦波
    ax2.plot(x, y2, 'r-', linewidth=2, label='0.5*sin(2x)')
    ax2.set_title('High Frequency Sine Wave')
    ax2.set_xlabel('x (radians)')
    ax2.set_ylabel('Amplitude')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 子图3: 低频正弦波
    ax3.plot(x, y3, 'g-', linewidth=2, label='1.5*sin(0.5x)')
    ax3.set_title('Low Frequency Sine Wave')
    ax3.set_xlabel('x (radians)')
    ax3.set_ylabel('Amplitude')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 子图4: 复合波形
    ax4.plot(x, y_combined, 'm-', linewidth=2, label='Combined Wave')
    ax4.plot(x, y1, 'b--', alpha=0.5, label='sin(x)')
    ax4.plot(x, y2, 'r--', alpha=0.5, label='0.5*sin(2x)')
    ax4.plot(x, y3, 'g--', alpha=0.5, label='1.5*sin(0.5x)')
    ax4.set_title('Combined Sine Waves')
    ax4.set_xlabel('x (radians)')
    ax4.set_ylabel('Amplitude')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
    plt.savefig(temp_file.name, dpi=150, bbox_inches='tight')
    plt.close()
    
    return temp_file.name

def setup_test_environment():
    """设置测试环境"""
    print("🔧 设置测试环境...")
    
    try:
        # 管理员登录
        login_response = requests.post(f"{BASE_URL}/admin/login", 
                                     json={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD})
        
        if login_response.status_code != 200:
            print(f"❌ 管理员登录失败: {login_response.status_code}")
            return None
            
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # 创建测试班级
        class_data = {
            "name": "物理实验班",
            "code": "PHY2024",
            "description": "正弦波实验班级"
        }
        
        class_response = requests.post(f"{BASE_URL}/admin/classes", 
                                     json=class_data, headers=headers)
        
        if class_response.status_code == 200:
            class_id = class_response.json()["id"]
            print(f"✅ 班级创建成功: ID {class_id}")
        else:
            # 可能班级已存在，获取现有班级
            classes_response = requests.get(f"{BASE_URL}/admin/classes", headers=headers)
            if classes_response.status_code == 200:
                classes = classes_response.json()
                if classes:
                    class_id = classes[0]["id"]
                    print(f"✅ 使用现有班级: ID {class_id}")
                else:
                    print("❌ 无法获取班级信息")
                    return None
            else:
                print("❌ 无法获取班级信息")
                return None
        
        # 激活班级
        activate_response = requests.patch(f"{BASE_URL}/admin/classes/{class_id}/activate", 
                                         headers=headers)
        if activate_response.status_code == 200:
            print("✅ 班级激活成功")
        
        # 创建学生
        student_data = {
            "class_id": class_id,
            "student_no": "PHY001",
            "name": "张三",
            "email": "<EMAIL>"
        }
        
        student_response = requests.post(f"{BASE_URL}/admin/students", 
                                       json=student_data, headers=headers)
        if student_response.status_code == 200:
            print("✅ 学生创建成功")
        
        # 创建实验
        question_data = {
            "class_id": class_id,
            "title": "正弦波分析实验",
            "description": "分析不同频率和振幅的正弦波特性，记录波形参数和测量数据",
            "experiment_type": "physics",
            "submission_type": "image_data",
            "is_open": True,
            "order": 1
        }
        
        question_response = requests.post(f"{BASE_URL}/admin/questions", 
                                        json=question_data, headers=headers)
        
        if question_response.status_code == 200:
            question_id = question_response.json()["id"]
            print(f"✅ 实验创建成功: ID {question_id}")
            return {"class_id": class_id, "question_id": question_id, "headers": headers}
        else:
            print(f"❌ 实验创建失败: {question_response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 设置测试环境失败: {e}")
        return None

def submit_sine_wave_data(setup_info, image_path, x, y_combined):
    """提交正弦波数据"""
    print("📤 提交正弦波实验数据...")
    
    try:
        # 准备提交数据
        # 选择一些关键数据点进行提交
        key_indices = [0, 25, 50, 75, 99]  # 选择5个关键点
        key_x_values = [float(x[i]) for i in key_indices]
        key_y_values = [float(y_combined[i]) for i in key_indices]
        
        # 计算统计数据
        amplitude_max = float(np.max(y_combined))
        amplitude_min = float(np.min(y_combined))
        amplitude_range = amplitude_max - amplitude_min
        frequency_estimate = 2.5  # 基于我们的复合波形
        
        # 准备数字数据 - 使用简单的数组格式
        numeric_data = [
            amplitude_max,      # 最大振幅
            amplitude_min,      # 最小振幅
            amplitude_range,    # 振幅范围
            frequency_estimate, # 估计频率
            float(len(x)),      # 数据点数
            key_x_values[0],    # 第一个关键点x
            key_y_values[0],    # 第一个关键点y
            key_x_values[2],    # 中间关键点x
            key_y_values[2],    # 中间关键点y
            key_x_values[4],    # 最后关键点x
            key_y_values[4]     # 最后关键点y
        ]
        
        # 提交数据
        with open(image_path, 'rb') as img_file:
            files = {"image": img_file}
            data = {
                "question_id": setup_info["question_id"],
                "student_no": "2022062030",
                "student_name": "吴可",
                "note": "正弦波分析实验 - 复合波形分析，包含基础波、高频波和低频波的叠加效果。数据含义：[最大振幅,最小振幅,振幅范围,频率,数据点数,关键点坐标...]",
                "numeric": json.dumps(numeric_data)
            }
            
            response = requests.post(f"{BASE_URL}/api/submissions", 
                                   files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                submission_data = response.json()
                print("✅ 数据提交成功!")
                print(f"   提交ID: {submission_data.get('id')}")
                print(f"   学生: {submission_data.get('student_name')}")
                print(f"   时间: {submission_data.get('created_at')}")
                return submission_data
            else:
                print(f"❌ 数据提交失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ 提交数据时出错: {e}")
        return None

def view_submission_results(setup_info):
    """查看提交结果"""
    print("👀 查看提交结果...")
    
    try:
        # 查看学生提交情况
        students_response = requests.get(f"{BASE_URL}/public/students")
        if students_response.status_code == 200:
            students_data = students_response.json()
            print(f"✅ 获取到 {len(students_data)} 个学生的提交情况")
            
            for student in students_data:
                print(f"   学生: {student['name']} ({student['student_no']})")
                print(f"   提交数: {student['submission_count']}")
                if student['submissions']:
                    latest = student['submissions'][0]
                    print(f"   最新提交: {latest['question_title']}")
                    print(f"   提交时间: {latest['created_at']}")
        
        # 查看公开展示
        showcase_response = requests.get(f"{BASE_URL}/public/showcase", 
                                       params={"question_id": setup_info["question_id"]})
        if showcase_response.status_code == 200:
            showcase_data = showcase_response.json()
            print(f"✅ 公开展示包含 {len(showcase_data)} 个可见提交")
            
            for submission in showcase_data:
                print(f"   提交者: {submission.get('student_name', '匿名')}")
                print(f"   评分: {submission.get('score', '未评分')}")
                print(f"   图片: {submission.get('image_url', '无')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查看结果时出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 正弦波实验测试开始")
    print("=" * 60)
    
    # 1. 生成正弦波数据
    x, y1, y2, y3, y_combined = generate_sine_wave_data()
    
    # 2. 创建图像
    image_path = create_sine_wave_plot(x, y1, y2, y3, y_combined)
    print(f"✅ 图像已保存: {image_path}")
    
    # 3. 设置测试环境
    setup_info = setup_test_environment()
    if not setup_info:
        print("❌ 测试环境设置失败，退出测试")
        return
    
    # 4. 提交数据
    submission = submit_sine_wave_data(setup_info, image_path, x, y_combined)
    if not submission:
        print("❌ 数据提交失败，退出测试")
        return
    
    # 5. 查看结果
    view_submission_results(setup_info)
    
    # 6. 清理临时文件
    try:
        os.unlink(image_path)
        print("✅ 临时文件已清理")
    except:
        pass
    
    print("\n🎉 正弦波实验测试完成!")
    print("🌐 访问以下地址查看结果:")
    print(f"   管理端: {BASE_URL}/static/admin.html")
    print(f"   学生情况: {BASE_URL}/public/students/view")
    print(f"   作业展示: {BASE_URL}/public/showcase/view")

if __name__ == "__main__":
    main()
